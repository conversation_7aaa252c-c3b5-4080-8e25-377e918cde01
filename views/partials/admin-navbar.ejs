<!-- Admin Navbar -->
<nav class="bg-purple-600 text-white shadow-lg">
  <div class="container mx-auto px-4">
    <div class="flex justify-between items-center h-16">
      <!-- Logo and Brand -->
      <div class="flex items-center space-x-4">
        <a href="/admin/dashboard" class="flex items-center h-10 overflow-hidden">
          <div class="flex items-center justify-center h-10 w-10 overflow-hidden">
            <% if (locals.siteSettings && siteSettings.site_logo) { %>
              <img src="<%= siteSettings.site_logo %>" alt="<%= __('app.name') %>" class="site-logo">
            <% } else { %>
              <span class="text-xl font-bold site-title"><%= __('app.name') %></span>
            <% } %>
          </div>
        </a>

        <!-- Main Navigation -->
        <div class="hidden md:flex space-x-4 ml-10">
          <a href="/admin/dashboard" class="px-3 py-2 rounded <%= currentPage === 'dashboard' ? 'bg-purple-700' : 'hover:bg-purple-700' %> transition flex items-center">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
            </svg>
            <%= __('admin.dashboard') %>
          </a>

          <!-- Users & Groups Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded <%= ['users', 'add-user', 'roles', 'groups'].includes(currentPage) ? 'bg-purple-700' : 'hover:bg-purple-700' %> transition flex items-center">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
              Users & Groups
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <!-- Added invisible bridge element to prevent dropdown from closing -->
              <div class="absolute h-2 w-full top-[-8px]"></div>
              <a href="/admin/users" class="block px-4 py-2 text-gray-800 hover:bg-purple-100"><%= __('admin.allUsers') %></a>
              <a href="/admin/users/pending-approvals" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">
                Pending Approvals
                <% if (locals.pendingApprovals && pendingApprovals > 0) { %>
                  <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-600 rounded-full ml-2"><%= pendingApprovals %></span>
                <% } %>
              </a>
              <a href="/admin/users/roles" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Manage Roles</a>
              <div class="border-t border-gray-200 my-1"></div>
              <a href="/admin/groups" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Manage Groups</a>
            </div>
          </div>

          <!-- Tests Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded <%= ['tests', 'add-test', 'test-categories'].includes(currentPage) ? 'bg-purple-700' : 'hover:bg-purple-700' %> transition flex items-center">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
              </svg>
              <%= __('admin.exams') %>
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <!-- Added invisible bridge element to prevent dropdown from closing -->
              <div class="absolute h-2 w-full top-[-8px]"></div>
              <a href="/admin/tests" class="block px-4 py-2 text-gray-800 hover:bg-purple-100"><%= __('admin.allTests') %></a>
              <a href="/admin/tests/add" class="block px-4 py-2 text-gray-800 hover:bg-purple-100"><%= __('admin.createTest') %></a>
              <a href="/admin/tests/categories" class="block px-4 py-2 text-gray-800 hover:bg-purple-100"><%= __('admin.testCategories') %></a>
              <a href="/admin/test-assignments" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Test Assignments</a>
              <div class="border-t border-gray-200 my-1"></div>
              <a href="/admin/access-requests" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">
                Access Requests
                <% if (locals.pendingAccessRequests && pendingAccessRequests > 0) { %>
                  <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-600 rounded-full ml-2"><%= pendingAccessRequests %></span>
                <% } %>
              </a>
            </div>
          </div>

          <!-- Students Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded <%= ['students', 'student-data', 'student-import', 'student-manage'].includes(currentPage) ? 'bg-purple-700' : 'hover:bg-purple-700' %> transition flex items-center">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
              </svg>
              Students
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <!-- Added invisible bridge element to prevent dropdown from closing -->
              <div class="absolute h-2 w-full top-[-8px]"></div>
              <a href="/admin/students/data" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Student Data</a>
              <a href="/admin/students/manage" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Manage Students</a>
              <a href="/admin/students/import" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Import Students</a>
              <div class="border-t border-gray-200 my-1"></div>
              <a href="/admin/students/import/template" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Download Template</a>
              <a href="/admin/students/import/results" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Import Results</a>
            </div>
          </div>

          <!-- Questions Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded <%= ['questions', 'add-question', 'import-questions', 'question-categories'].includes(currentPage) ? 'bg-purple-700' : 'hover:bg-purple-700' %> transition flex items-center">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <%= __('admin.questions') %>
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <!-- Added invisible bridge element to prevent dropdown from closing -->
              <div class="absolute h-2 w-full top-[-8px]"></div>
              <a href="/admin/questions" class="block px-4 py-2 text-gray-800 hover:bg-purple-100"><%= __('admin.questionBank') %></a>
              <a href="/admin/questions/add" class="block px-4 py-2 text-gray-800 hover:bg-purple-100"><%= __('admin.addQuestion') %></a>
              <a href="/admin/questions/import" class="block px-4 py-2 text-gray-800 hover:bg-purple-100"><%= __('admin.import') %> <%= __('admin.questions') %></a>
              <a href="/admin/questions/categories" class="block px-4 py-2 text-gray-800 hover:bg-purple-100"><%= __('admin.categories') %></a>
              <a href="/admin/questions/category-mappings" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Category Mappings</a>
              <div class="border-t border-gray-200 my-1"></div>
              <a href="/admin/questions/archived" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Archived Questions</a>
              <a href="/admin/questions/trash" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Deleted Questions</a>
            </div>
          </div>

          <!-- Calendar -->
          <a href="/admin/calendar" class="px-3 py-2 rounded <%= currentPage === 'calendar' ? 'bg-purple-700' : 'hover:bg-purple-700' %> transition flex items-center">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            Calendar
          </a>

          <!-- Database Schema -->
          <a href="/admin/schema" class="px-3 py-2 rounded <%= currentPage === 'schema' ? 'bg-purple-700' : 'hover:bg-purple-700' %> transition flex items-center">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path>
            </svg>
            Database Schema
          </a>

          <!-- Reports Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded <%= ['reports', 'export-reports'].includes(currentPage) ? 'bg-purple-700' : 'hover:bg-purple-700' %> transition flex items-center">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
              <%= __('admin.reports') %>
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <!-- Added invisible bridge element to prevent dropdown from closing -->
              <div class="absolute h-2 w-full top-[-8px]"></div>
              <a href="/admin/reports" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Reports Dashboard</a>
              <a href="/admin/reports/export" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Export Reports</a>
            </div>
          </div>

          <!-- Inventory Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded <%= ['inventory', 'inventory-items', 'inventory-categories', 'inventory-transactions'].includes(currentPage) ? 'bg-purple-700' : 'hover:bg-purple-700' %> transition flex items-center">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
              IT Inventory
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <!-- Added invisible bridge element to prevent dropdown from closing -->
              <div class="absolute h-2 w-full top-[-8px]"></div>
              <a href="/admin/inventory" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Dashboard</a>
              <a href="/admin/inventory/items" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Inventory Items</a>
              <a href="/admin/inventory/categories" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Categories</a>
              <div class="border-t border-gray-200 my-1"></div>
              <a href="/admin/inventory/transactions" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Transactions</a>
              <a href="/admin/inventory/transactions/issue" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Issue Item</a>
              <a href="/admin/inventory/transactions/receive" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Receive Item</a>
              <div class="border-t border-gray-200 my-1"></div>
              <a href="/admin/repair/vendors" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Repair Vendors</a>
              <a href="/admin/repair/history" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Repair History</a>
              <div class="border-t border-gray-200 my-1"></div>
              <a href="/admin/inventory/test-voucher" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Test Voucher</a>
              <div class="border-t border-gray-200 my-1"></div>
              <a href="/admin/inventory/reports" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Reports</a>
            </div>
          </div>

          <!-- IT Issues Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded <%= ['issues', 'admin-issues', 'report-issue'].includes(currentPage) ? 'bg-purple-700' : 'hover:bg-purple-700' %> transition flex items-center">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              IT Issues
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <!-- Added invisible bridge element to prevent dropdown from closing -->
              <div class="absolute h-2 w-full top-[-8px]"></div>
              <a href="/issues" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Issue Tracker</a>
              <a href="/issues/list" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">All Issues</a>
              <a href="/issues/report" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Report Issue</a>
              <div class="border-t border-gray-200 my-1"></div>
              <a href="/admin/issues/dashboard" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Admin Dashboard</a>
            </div>
          </div>

          <!-- Settings Dropdown -->
          <div class="relative group">
            <button class="px-3 py-2 rounded <%= ['settings', 'email-templates', 'security', 'backup'].includes(currentPage) ? 'bg-purple-700' : 'hover:bg-purple-700' %> transition flex items-center">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </button>
            <div class="absolute left-0 mt-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
              <!-- Added invisible bridge element to prevent dropdown from closing -->
              <div class="absolute h-2 w-full top-[-8px]"></div>
              <a href="/admin/settings" class="block px-4 py-2 text-gray-800 hover:bg-purple-100"><%= __('admin.generalSettings') %></a>
              <a href="/admin/settings/logo" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Site Logo</a>
              <a href="/admin/settings/email" class="block px-4 py-2 text-gray-800 hover:bg-purple-100"><%= __('admin.emailTemplates') %></a>
              <a href="/admin/settings/security" class="block px-4 py-2 text-gray-800 hover:bg-purple-100"><%= __('admin.security') %></a>
              <a href="/admin/settings/backup" class="block px-4 py-2 text-gray-800 hover:bg-purple-100"><%= __('admin.backup') %></a>
              <div class="border-t border-gray-200 my-1"></div>
              <a href="/admin/logs" class="block px-4 py-2 text-gray-800 hover:bg-purple-100"><%= __('admin.system') %> <%= __('admin.logs') %></a>
              <a href="/admin/error-logs" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Error Logs</a>
              <a href="/admin/query-logs" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Database Query Logs</a>
              <a href="/admin/schema" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Database Schema</a>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Side Menu -->
      <div class="flex items-center space-x-4">


        <!-- Notifications -->
        <div class="relative">
          <a href="/notifications" class="text-white">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
            </svg>
            <span id="adminNotificationCount" class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center notification-count hidden">0</span>
          </a>
        </div>

        <!-- User Menu and Logout -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <span class="hidden md:inline-block">
              <% if (locals.user) { %>
                <span class="font-semibold"><%= user.username || user.name || 'Admin' %></span>
              <% } else { %>
                Admin
              <% } %>
            </span>
            <div class="relative group">
              <button class="w-10 h-10 rounded-full bg-purple-500 flex items-center justify-center">
                <% if (locals.user && user.profile_image && user.profile_image.startsWith('/')) { %>
                  <img src="<%= user.profile_image %>" alt="Profile" class="w-10 h-10 rounded-full object-cover">
                <% } else if (locals.user && user.profile_image) { %>
                  <img src="/uploads/profiles/<%= user.profile_image %>" alt="Profile" class="w-10 h-10 rounded-full object-cover">
                <% } else { %>
                  <%
                    let initials = 'A';
                    if (locals.user && user.username) {
                      initials = user.username.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();
                    } else if (locals.user && user.name) {
                      initials = user.name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();
                    }
                  %>
                  <span class="text-lg font-bold"><%= initials %></span>
                <% } %>
              </button>
              <div class="absolute right-0 mt-0 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
                <!-- Added invisible bridge element to prevent dropdown from closing -->
                <div class="absolute h-2 w-full top-[-8px]"></div>
                <a href="/admin/profile" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">My Profile</a>
                <div class="border-t border-gray-200 my-1"></div>
                <a href="/help/admin" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Help Articles</a>
                <a href="/help/admin/create" class="block px-4 py-2 text-gray-800 hover:bg-purple-100">Create Help Article</a>
              </div>
            </div>
          </div>
          <a href="/logout" class="flex items-center space-x-2 px-3 py-2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg transition-colors">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            <span class="text-sm font-medium">Logout</span>
          </a>
        </div>

        <!-- Mobile Menu Button -->
        <div class="md:hidden">
          <button id="mobile-menu-button" class="text-white focus:outline-none">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div id="mobile-menu" class="hidden md:hidden bg-purple-700 pb-4 px-4 absolute w-full left-0 z-50">
    <a href="/admin/dashboard" class="block py-2 text-white <%= currentPage === 'dashboard' ? 'font-bold' : '' %>">Dashboard</a>

    <!-- Users Section -->
    <div class="py-2">
      <button id="mobile-users-toggle" class="flex justify-between items-center w-full text-white">
        <span>Users & Groups</span>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      <div id="mobile-users-menu" class="hidden pl-4 mt-2 space-y-2">
        <a href="/admin/users" class="block py-1 text-white">All Users</a>
        <a href="/admin/users/pending-approvals" class="block py-1 text-white">Pending Approvals
          <% if (locals.pendingApprovals && pendingApprovals > 0) { %>
            <span class="bg-red-600 text-white text-xs px-1 py-0.5 rounded-full ml-1"><%= pendingApprovals %></span>
          <% } %>
        </a>
        <a href="/admin/users/roles" class="block py-1 text-white">Manage Roles</a>
        <div class="border-t border-purple-600 my-1"></div>
        <a href="/admin/groups" class="block py-1 text-white">Manage Groups</a>
      </div>
    </div>

    <!-- Tests Section -->
    <div class="py-2">
      <button id="mobile-tests-toggle" class="flex justify-between items-center w-full text-white">
        <span>Tests</span>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      <div id="mobile-tests-menu" class="hidden pl-4 mt-2 space-y-2">
        <a href="/admin/tests" class="block py-1 text-white">All Tests</a>
        <a href="/admin/tests/add" class="block py-1 text-white">Create New Test</a>
        <a href="/admin/tests/categories" class="block py-1 text-white">Test Categories</a>
        <a href="/admin/test-assignments" class="block py-1 text-white">Test Assignments</a>
      </div>
    </div>

    <!-- Students Section -->
    <div class="py-2">
      <button id="mobile-students-toggle" class="flex justify-between items-center w-full text-white">
        <span>Students</span>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      <div id="mobile-students-menu" class="hidden pl-4 mt-2 space-y-2">
        <a href="/admin/students/data" class="block py-1 text-white">Student Data</a>
        <a href="/admin/students/manage" class="block py-1 text-white">Manage Students</a>
        <a href="/admin/students/import" class="block py-1 text-white">Import Students</a>
        <div class="border-t border-purple-600 my-1"></div>
        <a href="/admin/students/import/template" class="block py-1 text-white">Download Template</a>
        <a href="/admin/students/import/results" class="block py-1 text-white">Import Results</a>
      </div>
    </div>

    <!-- Questions Section -->
    <div class="py-2">
      <button id="mobile-questions-toggle" class="flex justify-between items-center w-full text-white">
        <span>Questions</span>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      <div id="mobile-questions-menu" class="hidden pl-4 mt-2 space-y-2">
        <a href="/admin/questions" class="block py-1 text-white">Question Bank</a>
        <a href="/admin/questions/add" class="block py-1 text-white">Add Question</a>
        <a href="/admin/questions/import" class="block py-1 text-white">Import Questions</a>
        <a href="/admin/questions/categories" class="block py-1 text-white">Categories</a>
        <a href="/admin/questions/category-mappings" class="block py-1 text-white">Category Mappings</a>
        <div class="border-t border-purple-600 my-1"></div>
        <a href="/admin/questions/archived" class="block py-1 text-white">Archived Questions</a>
        <a href="/admin/questions/trash" class="block py-1 text-white">Deleted Questions</a>
      </div>
    </div>

    <!-- Calendar -->
    <a href="/admin/calendar" class="block py-2 text-white">Calendar</a>

    <!-- Database Schema -->
    <a href="/admin/schema" class="block py-2 text-white">Database Schema</a>

    <!-- Reports Section -->
    <div class="py-2">
      <button id="mobile-reports-toggle" class="flex justify-between items-center w-full text-white">
        <span>Reports</span>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      <div id="mobile-reports-menu" class="hidden pl-4 mt-2 space-y-2">
        <a href="/admin/reports" class="block py-1 text-white">Reports Dashboard</a>
        <a href="/admin/reports/export" class="block py-1 text-white">Export Reports</a>
      </div>
    </div>

    <!-- Inventory Section -->
    <div class="py-2">
      <button id="mobile-inventory-toggle" class="flex justify-between items-center w-full text-white">
        <span>IT Inventory</span>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      <div id="mobile-inventory-menu" class="hidden pl-4 mt-2 space-y-2">
        <a href="/admin/inventory" class="block py-1 text-white">Dashboard</a>
        <a href="/admin/inventory/items" class="block py-1 text-white">Inventory Items</a>
        <a href="/admin/inventory/categories" class="block py-1 text-white">Categories</a>
        <div class="border-t border-purple-600 my-1"></div>
        <a href="/admin/inventory/transactions" class="block py-1 text-white">Transactions</a>
        <a href="/admin/inventory/transactions/issue" class="block py-1 text-white">Issue Item</a>
        <a href="/admin/inventory/transactions/receive" class="block py-1 text-white">Receive Item</a>
        <div class="border-t border-purple-600 my-1"></div>
        <a href="/admin/repair/vendors" class="block py-1 text-white">Repair Vendors</a>
        <a href="/admin/repair/history" class="block py-1 text-white">Repair History</a>
        <div class="border-t border-purple-600 my-1"></div>
        <a href="/admin/inventory/test-voucher" class="block py-1 text-white">Test Voucher</a>
        <div class="border-t border-purple-600 my-1"></div>
        <a href="/admin/inventory/reports" class="block py-1 text-white">Reports</a>
      </div>
    </div>

    <!-- IT Issues Section -->
    <div class="py-2">
      <button id="mobile-issues-toggle" class="flex justify-between items-center w-full text-white">
        <span>IT Issues</span>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      <div id="mobile-issues-menu" class="hidden pl-4 mt-2 space-y-2">
        <a href="/issues" class="block py-1 text-white">Issue Tracker</a>
        <a href="/issues/list" class="block py-1 text-white">All Issues</a>
        <a href="/issues/report" class="block py-1 text-white">Report Issue</a>
        <div class="border-t border-purple-600 my-1"></div>
        <a href="/admin/issues/dashboard" class="block py-1 text-white">Admin Dashboard</a>
      </div>
    </div>

    <!-- Settings Section -->
    <div class="py-2">
      <button id="mobile-settings-toggle" class="flex justify-between items-center w-full text-white">
        <span>Settings</span>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      <div id="mobile-settings-menu" class="hidden pl-4 mt-2 space-y-2">
        <a href="/admin/settings" class="block py-1 text-white">General Settings</a>
        <a href="/admin/settings/logo" class="block py-1 text-white">Site Logo</a>
        <a href="/admin/settings/email" class="block py-1 text-white">Email Templates</a>
        <a href="/admin/settings/security" class="block py-1 text-white">Security</a>
        <a href="/admin/settings/backup" class="block py-1 text-white">Backup & Restore</a>
        <div class="border-t border-purple-600 my-1"></div>
        <a href="/admin/logs" class="block py-1 text-white">System Logs</a>
        <a href="/admin/error-logs" class="block py-1 text-white">Error Logs</a>
        <a href="/admin/query-logs" class="block py-1 text-white">Database Query Logs</a>
        <a href="/admin/schema" class="block py-1 text-white">Database Schema</a>
      </div>
    </div>

    <div class="border-t border-purple-600 my-2"></div>
    <a href="/admin/profile" class="block py-2 text-white">My Profile</a>
    <div class="border-t border-purple-600 my-2"></div>
    <a href="/help/admin" class="block py-2 text-white">Help Articles</a>
    <a href="/help/admin/create" class="block py-2 text-white">Create Help Article</a>
    <a href="/notifications" class="block py-2 text-white flex items-center relative">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
      </svg>
      Notifications
      <span class="mobile-admin-notification-count absolute top-0 left-5 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center hidden">0</span>
    </a>




  </div>
</nav>

<!-- Breadcrumbs -->
<% if (locals.breadcrumbs && breadcrumbs.length > 0) { %>
<div class="bg-gray-100 py-2 px-4">
  <div class="container mx-auto">
    <div class="flex items-center text-sm text-gray-600">
      <% breadcrumbs.forEach((crumb, index) => { %>
        <% if (index > 0) { %>
          <svg class="w-3 h-3 mx-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
          </svg>
        <% } %>

        <% if (crumb.active) { %>
          <span class="text-gray-800"><%= crumb.text %></span>
        <% } else { %>
          <a href="<%= crumb.url %>" class="hover:text-indigo-600"><%= crumb.text %></a>
        <% } %>
      <% }); %>
    </div>
  </div>
</div>
<% } %>

<!-- Mobile Menu Toggle Script -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Main mobile menu toggle
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener('click', function() {
        mobileMenu.classList.toggle('hidden');
      });
    }

    // Submenu toggles
    const toggles = [
      { button: 'mobile-users-toggle', menu: 'mobile-users-menu' },
      { button: 'mobile-tests-toggle', menu: 'mobile-tests-menu' },
      { button: 'mobile-students-toggle', menu: 'mobile-students-menu' },
      { button: 'mobile-questions-toggle', menu: 'mobile-questions-menu' },
      { button: 'mobile-reports-toggle', menu: 'mobile-reports-menu' },
      { button: 'mobile-inventory-toggle', menu: 'mobile-inventory-menu' },
      { button: 'mobile-issues-toggle', menu: 'mobile-issues-menu' },
      { button: 'mobile-system-toggle', menu: 'mobile-system-menu' },
      { button: 'mobile-settings-toggle', menu: 'mobile-settings-menu' }
    ];

    toggles.forEach(item => {
      const button = document.getElementById(item.button);
      const menu = document.getElementById(item.menu);

      if (button && menu) {
        button.addEventListener('click', function() {
          menu.classList.toggle('hidden');
        });
      }
    });


  });
</script>