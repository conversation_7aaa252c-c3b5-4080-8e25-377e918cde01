<!-- Infrastructure Command Center -->
<div class="mb-8">
    <div class="executive-card text-white rounded-xl p-8 shadow-2xl">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold mb-2">Infrastructure Command Center</h1>
                <p class="text-blue-100 text-lg">Real-time facility monitoring and resource management</p>
            </div>
            <div class="text-right">
                <div class="leadership-badge px-4 py-2 rounded-full">
                    <i class="fas fa-building mr-2"></i>
                    <span class="font-bold">FACILITY OVERSIGHT</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Infrastructure Overview -->
<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
    <!-- Classroom Status -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-primary">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-principal-light rounded-lg mr-3">
                        <i class="fas fa-door-open text-principal-primary text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-primary uppercase tracking-wide">Classrooms</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark"><%= classroomData.length %></p>
                <p class="text-sm text-principal-silver">Total Available</p>
                <p class="text-xs text-gray-500 mt-1">Capacity: 50 students each</p>
            </div>
        </div>
    </div>

    <!-- IT Equipment -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-secondary">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-blue-50 rounded-lg mr-3">
                        <i class="fas fa-laptop text-principal-secondary text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-secondary uppercase tracking-wide">IT Equipment</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">156</p>
                <p class="text-sm text-principal-silver">Devices Active</p>
            </div>
        </div>
    </div>

    <!-- Maintenance Status -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-accent">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-yellow-50 rounded-lg mr-3">
                        <i class="fas fa-tools text-principal-accent text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-principal-accent uppercase tracking-wide">Maintenance</span>
                </div>
                <p class="text-3xl font-bold text-principal-dark">98%</p>
                <p class="text-sm text-principal-silver">Operational</p>
            </div>
        </div>
    </div>

    <!-- Student Capacity -->
    <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="p-2 bg-green-50 rounded-lg mr-3">
                        <i class="fas fa-users text-green-500 text-lg"></i>
                    </div>
                    <span class="text-xs font-semibold text-green-500 uppercase tracking-wide">Capacity</span>
                </div>
                <%
                const totalCapacity = classroomData.length * 50;
                const totalStudents = classroomData.reduce((sum, room) => sum + (room.student_count || 0), 0);
                const overallUtilization = totalCapacity > 0 ? Math.round((totalStudents / totalCapacity) * 100) : 0;
                %>
                <p class="text-3xl font-bold text-principal-dark"><%= totalStudents %>/<%= totalCapacity %></p>
                <p class="text-sm text-principal-silver">Students (<%= overallUtilization %>% Utilized)</p>
            </div>
        </div>
    </div>
</div>

<!-- Facility Management Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Classroom Management -->
    <div class="bg-white rounded-xl shadow-lg border border-principal-light">
        <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-6 rounded-t-xl">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-chalkboard text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Classroom Operations</h2>
            </div>
        </div>
        <div class="p-6">
            <!-- Classroom Summary Stats -->
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 rounded-lg p-3 text-center">
                    <div class="text-lg font-bold text-blue-600"><%= classroomData.length %></div>
                    <div class="text-xs text-blue-500">Total Rooms</div>
                </div>
                <div class="bg-green-50 rounded-lg p-3 text-center">
                    <div class="text-lg font-bold text-green-600"><%= classroomData.filter(room => room.classroom_id).length %></div>
                    <div class="text-xs text-green-500">Operational</div>
                </div>
                <div class="bg-yellow-50 rounded-lg p-3 text-center">
                    <div class="text-lg font-bold text-yellow-600"><%= classroomData.reduce((sum, room) => sum + (room.student_count || 0), 0) %></div>
                    <div class="text-xs text-yellow-500">Students</div>
                </div>
                <div class="bg-purple-50 rounded-lg p-3 text-center">
                    <div class="text-lg font-bold text-purple-600"><%= Math.round(classroomData.reduce((sum, room) => sum + (room.utilization_percentage || 0), 0) / classroomData.length) %>%</div>
                    <div class="text-xs text-purple-500">Avg. Utilization</div>
                </div>
            </div>

            <!-- Scrollable Classroom List -->
            <div class="classroom-list-container" style="max-height: 400px; overflow-y: auto;">
                <div class="space-y-3">
                    <% classroomData.forEach((room, index) => { %>
                        <div id="classroom-<%= room.room_id %>" class="flex items-center justify-between p-3 bg-gradient-to-r from-principal-light to-white rounded-lg border-l-4 border-principal-primary hover:shadow-md transition-all cursor-pointer classroom-card"
                             data-room="<%= room.room_id %>">
                            <div class="flex items-center flex-1">
                                <div class="w-8 h-8 bg-gradient-to-br from-principal-primary to-principal-secondary rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                    <span class="text-white text-xs font-bold"><%= room.room_id %></span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between">
                                        <p class="text-sm font-bold text-principal-dark truncate"><%= room.room_number %></p>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-bold <%= room.classroom_id ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %> ml-2 flex-shrink-0">
                                            <i class="fas <%= room.classroom_id ? 'fa-check-circle' : 'fa-circle' %> mr-1"></i>
                                            <%= room.classroom_id ? 'OP' : 'AV' %>
                                        </span>
                                    </div>
                                    <% if (room.grade && room.trade_name && room.section) { %>
                                        <p class="text-xs text-principal-silver truncate"><%= room.grade %> <%= room.trade_name %> <%= room.section %></p>
                                        <div class="flex items-center mt-1">
                                            <span class="text-xs text-principal-silver mr-2 flex-shrink-0">
                                                <%= room.student_count %>/<%= room.capacity || 50 %>
                                            </span>
                                            <div class="w-12 h-1.5 bg-gray-200 rounded-full mr-2 flex-shrink-0">
                                                <div class="h-1.5 rounded-full utilization-bar <%= (room.utilization_percentage || 0) > 90 ? 'bg-red-500' : (room.utilization_percentage || 0) > 75 ? 'bg-yellow-500' : 'bg-green-500' %>"
                                                     data-utilization="<%= Math.min(room.utilization_percentage || 0, 100) %>"></div>
                                            </div>
                                            <span class="text-xs font-medium <%= (room.utilization_percentage || 0) > 90 ? 'text-red-600' : (room.utilization_percentage || 0) > 75 ? 'text-yellow-600' : 'text-green-600' %> flex-shrink-0">
                                                <%= room.utilization_percentage || 0 %>%
                                            </span>
                                        </div>
                                    <% } else { %>
                                        <p class="text-xs text-principal-silver">
                                            Unassigned •
                                            <% if (room.floor === 0) { %>Ground Floor<% } else if (room.floor === 1) { %>First Floor<% } else { %>Floor <%= room.floor %><% } %>
                                        </p>
                                        <p class="text-xs text-gray-400">Capacity: <%= room.capacity || 50 %> Students</p>
                                    <% } %>
                                </div>
                            </div>
                        </div>
                    <% }); %>
                </div>
            </div>
        </div>
    </div>

    <!-- IT Equipment Status -->
    <div class="bg-white rounded-xl shadow-lg border border-principal-light">
        <div class="bg-gradient-to-r from-principal-secondary to-blue-600 text-white p-6 rounded-t-xl">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-desktop text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">IT Infrastructure</h2>
            </div>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <%
                const equipment = [
                    { name: 'Projectors', count: 24, status: 'operational', icon: 'fa-video' },
                    { name: 'Laptops', count: 48, status: 'operational', icon: 'fa-laptop' },
                    { name: 'Sound Systems', count: 24, status: 'operational', icon: 'fa-volume-up' },
                    { name: 'Interactive Boards', count: 12, status: 'operational', icon: 'fa-chalkboard-teacher' },
                    { name: 'Network Points', count: 96, status: 'operational', icon: 'fa-network-wired' },
                    { name: 'Security Cameras', count: 32, status: 'operational', icon: 'fa-video' }
                ];
                %>
                <% equipment.forEach(item => { %>
                    <div class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-white rounded-lg border-l-4 border-blue-500">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-3">
                                <i class="fas <%= item.icon %> text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="text-sm font-bold text-principal-dark"><%= item.name %></p>
                                <p class="text-xs text-principal-silver"><%= item.count %> units</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-green-100 text-green-800">
                                <i class="fas fa-check-circle mr-1"></i>
                                ACTIVE
                            </span>
                        </div>
                    </div>
                <% }); %>
            </div>
        </div>
    </div>
</div>

<!-- Special Facilities Grid -->
<div class="mt-8">
    <!-- Labs Section - Computer Labs and Science Labs Side by Side -->
    <div class="mb-8">
        <div class="bg-gradient-to-r from-blue-500 to-green-500 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <i class="fas fa-flask text-white text-xl mr-3"></i>
                <h2 class="text-xl font-bold text-white">Laboratory Facilities</h2>
                <span class="ml-3 text-blue-100 text-sm">Technology & Research Centers</span>
            </div>
        </div>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Computer Labs -->
            <div class="bg-white rounded-xl shadow-lg border border-principal-light">
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6 rounded-t-xl">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-desktop text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Computer Labs</h2>
            </div>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <% if (computerLabs && computerLabs.length > 0) { %>
                    <% computerLabs.forEach(lab => { %>
                        <div id="lab-<%= lab.room_id %>" class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-white rounded-lg border-l-4 border-blue-500 hover:shadow-md transition-all cursor-pointer classroom-card"
                             data-room="<%= lab.room_id %>">
                            <div class="flex items-center flex-1">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-desktop text-white text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-bold text-principal-dark"><%= lab.room_number %></p>
                                    <p class="text-xs text-principal-silver">
                                        <% if (lab.floor === 0) { %>
                                            Ground Floor • Capacity: <%= lab.capacity || 30 %> students
                                        <% } else if (lab.floor === 1) { %>
                                            First Floor • Capacity: <%= lab.capacity || 30 %> students
                                        <% } else if (lab.floor === 2) { %>
                                            Second Floor • Capacity: <%= lab.capacity || 30 %> students
                                        <% } else { %>
                                            Floor <%= lab.floor || 'N/A' %> • Capacity: <%= lab.capacity || 30 %> students
                                        <% } %>
                                    </p>
                                    <% if (lab.earliest_purchase) { %>
                                        <p class="text-xs text-gray-500 mt-1">
                                            <i class="fas fa-calendar-alt mr-1"></i>
                                            Purchased: <%= new Date(lab.earliest_purchase).toLocaleDateString() %>
                                            <% if (lab.latest_purchase && lab.latest_purchase !== lab.earliest_purchase) { %>
                                                - <%= new Date(lab.latest_purchase).toLocaleDateString() %>
                                            <% } %>
                                        </p>
                                    <% } %>
                                    <div class="flex items-center mt-1 space-x-3">
                                        <% if (lab.desktops > 0) { %>
                                            <span class="text-xs text-blue-600 font-medium">
                                                <i class="fas fa-desktop mr-1"></i><%= lab.desktops %> PCs
                                            </span>
                                        <% } %>
                                        <% if (lab.printers > 0) { %>
                                            <span class="text-xs text-green-600 font-medium">
                                                <i class="fas fa-print mr-1"></i><%= lab.printers %> Printers
                                            </span>
                                        <% } %>
                                        <% if (lab.ups_units > 0) { %>
                                            <span class="text-xs text-yellow-600 font-medium">
                                                <i class="fas fa-battery-full mr-1"></i><%= lab.ups_units %> UPS
                                            </span>
                                        <% } %>
                                        <% if (lab.cameras > 0) { %>
                                            <span class="text-xs text-red-600 font-medium">
                                                <i class="fas fa-camera mr-1"></i><%= lab.cameras %> Cameras
                                            </span>
                                        <% } %>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-blue-100 text-blue-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    <%= lab.total_equipment %> ITEMS
                                </span>
                            </div>
                        </div>
                    <% }); %>
                <% } else { %>
                    <div class="text-center py-8">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-desktop text-blue-500 text-lg"></i>
                        </div>
                        <p class="text-sm text-gray-500">No computer labs configured</p>
                    </div>
                <% } %>
            </div>
        </div>
        </div>

            <!-- Science Labs -->
            <div class="bg-white rounded-xl shadow-lg border border-principal-light">
        <div class="bg-gradient-to-r from-green-600 to-emerald-600 text-white p-6 rounded-t-xl">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-flask text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Science Labs</h2>
            </div>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <% if (scienceLabs && scienceLabs.length > 0) { %>
                    <% scienceLabs.forEach(lab => { %>
                        <div id="lab-<%= lab.room_id %>" class="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-white rounded-lg border-l-4 border-green-500 hover:shadow-md transition-all cursor-pointer classroom-card"
                             data-room="<%= lab.room_id %>">
                            <div class="flex items-center flex-1">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-flask text-white text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-bold text-principal-dark"><%= lab.room_number %></p>
                                    <p class="text-xs text-principal-silver">Capacity: <%= lab.capacity || 25 %> students</p>
                                    <div class="flex items-center mt-1 space-x-3">
                                        <% if (lab.projectors > 0) { %>
                                            <span class="text-xs text-purple-600 font-medium">
                                                <i class="fas fa-video mr-1"></i><%= lab.projectors %> Projectors
                                            </span>
                                        <% } %>
                                        <% if (lab.desktops > 0) { %>
                                            <span class="text-xs text-blue-600 font-medium">
                                                <i class="fas fa-desktop mr-1"></i><%= lab.desktops %> PCs
                                            </span>
                                        <% } %>
                                        <% if (lab.ups_units > 0) { %>
                                            <span class="text-xs text-yellow-600 font-medium">
                                                <i class="fas fa-battery-full mr-1"></i><%= lab.ups_units %> UPS
                                            </span>
                                        <% } %>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    <%= lab.total_equipment %> ITEMS
                                </span>
                            </div>
                        </div>
                    <% }); %>
                <% } else { %>
                    <div class="text-center py-8">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-flask text-green-500 text-lg"></i>
                        </div>
                        <p class="text-sm text-gray-500">No science labs configured</p>
                    </div>
                <% } %>
            </div>
        </div>
        </div>
    </div>
    </div>

    <!-- Library Section -->
    <div class="mb-8">
        <div class="bg-gradient-to-r from-purple-500 to-violet-600 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <i class="fas fa-book text-white text-xl mr-3"></i>
                <h2 class="text-xl font-bold text-white">Library</h2>
                <span class="ml-3 text-purple-100 text-sm">Knowledge Center</span>
            </div>
        </div>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="bg-white rounded-xl shadow-lg border border-principal-light">
        <div class="bg-gradient-to-r from-purple-600 to-violet-600 text-white p-6 rounded-t-xl">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-book text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Library</h2>
            </div>
        </div>
        <div class="p-6">
            <% if (library) { %>
                <div id="library-<%= library.room_id %>" class="flex items-center justify-between p-4 bg-gradient-to-r from-purple-50 to-white rounded-lg border-l-4 border-purple-500 hover:shadow-md transition-all cursor-pointer classroom-card"
                     data-room="<%= library.room_id %>">
                    <div class="flex items-center flex-1">
                        <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-book text-white text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-bold text-principal-dark"><%= library.room_number %></p>
                            <p class="text-xs text-principal-silver">Capacity: <%= library.capacity || 50 %> students</p>
                            <div class="flex items-center mt-1 space-x-3">
                                <% if (library.desktops > 0) { %>
                                    <span class="text-xs text-blue-600 font-medium">
                                        <i class="fas fa-desktop mr-1"></i><%= library.desktops %> PCs
                                    </span>
                                <% } %>
                                <% if (library.printers > 0) { %>
                                    <span class="text-xs text-green-600 font-medium">
                                        <i class="fas fa-print mr-1"></i><%= library.printers %> Printers
                                    </span>
                                <% } %>
                                <% if (library.ups_units > 0) { %>
                                    <span class="text-xs text-yellow-600 font-medium">
                                        <i class="fas fa-battery-full mr-1"></i><%= library.ups_units %> UPS
                                    </span>
                                <% } %>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-purple-100 text-purple-800">
                            <i class="fas fa-check-circle mr-1"></i>
                            <%= library.total_equipment %> ITEMS
                        </span>
                    </div>
                </div>
            <% } else { %>
                <div class="text-center py-8">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-book text-purple-500 text-lg"></i>
                    </div>
                    <p class="text-sm text-gray-500">Library not configured</p>
                </div>
            <% } %>
        </div>
        </div>
    </div>

    <!-- Administrative Offices Section -->
    <div class="mb-8">
        <div class="bg-gradient-to-r from-amber-500 to-orange-600 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <i class="fas fa-building text-white text-xl mr-3"></i>
                <h2 class="text-xl font-bold text-white">Administrative Offices</h2>
                <span class="ml-3 text-amber-100 text-sm">Management Centers</span>
            </div>
        </div>
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="bg-white rounded-xl shadow-lg border border-principal-light">
        <div class="bg-gradient-to-r from-amber-600 to-orange-600 text-white p-6 rounded-t-xl">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-building text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Administrative Offices</h2>
            </div>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <% if (offices && offices.length > 0) { %>
                    <% offices.forEach(office => { %>
                        <div id="office-<%= office.room_id %>" class="flex items-center justify-between p-4 bg-gradient-to-r from-amber-50 to-white rounded-lg border-l-4 border-amber-500 hover:shadow-md transition-all cursor-pointer classroom-card"
                             data-room="<%= office.room_id %>">
                            <div class="flex items-center flex-1">
                                <div class="w-10 h-10 bg-gradient-to-br from-amber-500 to-amber-600 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-user-tie text-white text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-bold text-principal-dark"><%= office.room_number %></p>
                                    <p class="text-xs text-principal-silver">
                                        <% if (office.room_number && office.room_number.toLowerCase().includes('principal')) { %>
                                            Staff Capacity: 10 personnel
                                        <% } else if (office.room_number && (office.room_number.toLowerCase().includes('admin') || office.room_number.toLowerCase().includes('computer office'))) { %>
                                            Staff Capacity: 3 personnel
                                        <% } else { %>
                                            Administrative facility
                                        <% } %>
                                    </p>
                                    <div class="flex items-center mt-1 space-x-3">
                                        <% if (office.room_number && (office.room_number.toLowerCase().includes('admin') || office.room_number.toLowerCase().includes('computer office'))) { %>
                                            <!-- Admin Office shows combined equipment: 3 computers + 2 printers -->
                                            <span class="text-xs text-blue-600 font-medium">
                                                <i class="fas fa-desktop mr-1"></i>3 PCs
                                            </span>
                                            <span class="text-xs text-green-600 font-medium">
                                                <i class="fas fa-print mr-1"></i>2 Printers
                                            </span>
                                        <% } else { %>
                                            <!-- Other offices show actual equipment counts -->
                                            <% if (office.desktops > 0) { %>
                                                <span class="text-xs text-blue-600 font-medium">
                                                    <i class="fas fa-desktop mr-1"></i><%= office.desktops %> PCs
                                                </span>
                                            <% } %>
                                            <% if (office.laptops > 0) { %>
                                                <span class="text-xs text-indigo-600 font-medium">
                                                    <i class="fas fa-laptop mr-1"></i><%= office.laptops %> Laptops
                                                </span>
                                            <% } %>
                                            <% if (office.printers > 0) { %>
                                                <span class="text-xs text-green-600 font-medium">
                                                    <i class="fas fa-print mr-1"></i><%= office.printers %> Printers
                                                </span>
                                            <% } %>
                                            <% if (office.tablets > 0) { %>
                                                <span class="text-xs text-pink-600 font-medium">
                                                    <i class="fas fa-tablet mr-1"></i><%= office.tablets %> Tablets
                                                </span>
                                            <% } %>
                                        <% } %>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-amber-100 text-amber-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    <%= office.total_equipment %> ITEMS
                                </span>
                            </div>
                        </div>
                    <% }); %>
                <% } else { %>
                    <div class="text-center py-8">
                        <div class="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-building text-amber-500 text-lg"></i>
                        </div>
                        <p class="text-sm text-gray-500">No offices configured</p>
                    </div>
                <% } %>
            </div>
        </div>
        </div>
    </div>

    <!-- Hostel Facilities Section -->
    <div class="mb-8">
        <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <i class="fas fa-bed text-white text-xl mr-3"></i>
                <h2 class="text-xl font-bold text-white">Hostel Facilities</h2>
                <span class="ml-3 text-indigo-100 text-sm">Residential Services</span>
            </div>
        </div>
        <div class="bg-white rounded-xl shadow-lg border border-principal-light">
        <div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-6 rounded-t-xl">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-bed text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Hostel Facilities</h2>
            </div>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <% if (hostelRooms && hostelRooms.length > 0) { %>
                    <% hostelRooms.forEach(room => { %>
                        <div id="hostel-<%= room.room_id %>" class="flex items-center justify-between p-4 bg-gradient-to-r from-indigo-50 to-white rounded-lg border-l-4 border-indigo-500 hover:shadow-md transition-all cursor-pointer classroom-card"
                             data-room="<%= room.room_id %>">
                            <div class="flex items-center flex-1">
                                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center mr-3">
                                    <% if (room.room_number.includes('Boys Hostel Warden Room')) { %>
                                        <i class="fas fa-male text-white text-sm"></i>
                                    <% } else if (room.room_number.includes('Girls Hostel Warden Room')) { %>
                                        <i class="fas fa-female text-white text-sm"></i>
                                    <% } else { %>
                                        <i class="fas fa-bed text-white text-sm"></i>
                                    <% } %>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-bold text-principal-dark">
                                        <% if (room.room_number.includes('Boys Hostel Warden Room')) { %>
                                            Boys Hostel Warden
                                        <% } else if (room.room_number.includes('Girls Hostel Warden Room')) { %>
                                            Girls Hostel Warden
                                        <% } else { %>
                                            <%= room.room_number %>
                                        <% } %>
                                    </p>
                                    <p class="text-xs text-principal-silver">
                                        <% if (room.floor === 0) { %>
                                            Ground Floor • <%= room.building %>
                                        <% } else if (room.floor === 1) { %>
                                            First Floor • <%= room.building %>
                                        <% } else if (room.floor === 2) { %>
                                            Second Floor • <%= room.building %>
                                        <% } else { %>
                                            Floor <%= room.floor || 'N/A' %> • <%= room.building %>
                                        <% } %>
                                    </p>
                                    <% if (room.earliest_purchase) { %>
                                        <p class="text-xs text-gray-500 mt-1">
                                            <i class="fas fa-calendar-alt mr-1"></i>
                                            Purchased: <%= new Date(room.earliest_purchase).toLocaleDateString() %>
                                            <% if (room.latest_purchase && room.latest_purchase !== room.earliest_purchase) { %>
                                                - <%= new Date(room.latest_purchase).toLocaleDateString() %>
                                            <% } %>
                                        </p>
                                    <% } %>
                                    <div class="flex items-center mt-1 space-x-3">
                                        <% if (room.desktops > 0) { %>
                                            <span class="text-xs text-blue-600 font-medium">
                                                <i class="fas fa-desktop mr-1"></i><%= room.desktops %> PCs
                                            </span>
                                        <% } %>
                                        <% if (room.tvs > 0) { %>
                                            <span class="text-xs text-purple-600 font-medium">
                                                <i class="fas fa-tv mr-1"></i><%= room.tvs %> TVs
                                            </span>
                                        <% } %>
                                        <% if (room.ups_units > 0) { %>
                                            <span class="text-xs text-yellow-600 font-medium">
                                                <i class="fas fa-battery-full mr-1"></i><%= room.ups_units %> UPS
                                            </span>
                                        <% } %>
                                        <% if (room.printers > 0) { %>
                                            <span class="text-xs text-green-600 font-medium">
                                                <i class="fas fa-print mr-1"></i><%= room.printers %> Printers
                                            </span>
                                        <% } %>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-indigo-100 text-indigo-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    <%= room.total_equipment %> ITEMS
                                </span>
                            </div>
                        </div>
                    <% }); %>
                <% } else { %>
                    <div class="col-span-full text-center py-8">
                        <div class="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-bed text-indigo-500 text-lg"></i>
                        </div>
                        <p class="text-sm text-gray-500">No hostel facilities configured</p>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</div>

<!-- Maintenance Schedule -->
<div class="mt-8 bg-white rounded-xl shadow-lg border border-principal-light">
    <div class="bg-gradient-to-r from-principal-accent to-principal-gold text-white p-6 rounded-t-xl">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                    <i class="fas fa-calendar-check text-lg"></i>
                </div>
                <h2 class="text-xl font-bold">Maintenance Schedule</h2>
            </div>
            <button class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg text-sm font-bold transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Schedule Maintenance
            </button>
        </div>
    </div>
    <div class="p-6">
        <div class="text-center py-8">
            <div class="w-16 h-16 bg-gradient-to-br from-principal-accent to-principal-gold rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-calendar-check text-white text-2xl"></i>
            </div>
            <h3 class="text-lg font-bold text-principal-dark">All Systems Operational</h3>
            <p class="text-sm text-principal-silver">No maintenance scheduled. All infrastructure running optimally.</p>
        </div>
    </div>
</div>

<!-- Classroom Details Modal -->
<div id="classroomModal" class="fixed inset-0 hidden z-50" style="background-color: rgba(0, 0, 0, 0.7);">
    <div class="flex items-center justify-center min-h-screen p-2 lg:p-3">
        <div class="bg-white rounded-lg shadow-2xl w-full max-h-screen overflow-hidden modal-container">
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4 lg:p-5">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                            <i class="fas fa-door-open text-lg"></i>
                        </div>
                        <div>
                            <h2 class="text-xl lg:text-2xl font-bold" id="modalTitle">Classroom Details</h2>
                            <p class="text-blue-100 text-sm" id="modalSubtitle">Complete classroom information</p>
                        </div>
                    </div>
                    <button id="closeModalBtn" class="text-white hover:text-gray-200 p-2 hover:bg-white hover:bg-opacity-10 rounded-lg transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- Modal Content -->
            <div class="overflow-y-auto" style="max-height: calc(100vh - 140px); background-color: white;" id="modalContent">
                <!-- Content will be loaded dynamically -->
                <div class="p-4 lg:p-6" style="background-color: white;">
                    <div class="text-center py-8">
                        <div class="w-12 h-12 lg:w-16 lg:h-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-door-open text-white text-lg lg:text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-700 mb-2">Select a Classroom</h3>
                        <p class="text-gray-500 text-sm">Click on any classroom card to view detailed information</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Animation Styles -->
<style>
    /* Modal Animation */
    #classroomModal:not(.hidden) .modal-content {
        animation: modalSlideIn 0.3s ease-out forwards;
    }

    #classroomModal.hidden .modal-content {
        animation: modalSlideOut 0.2s ease-in forwards;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: scale(0.9) translateY(-20px);
        }
        to {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }

    @keyframes modalSlideOut {
        from {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
        to {
            opacity: 0;
            transform: scale(0.9) translateY(-20px);
        }
    }

    /* Custom Scrollbar */
    #modalContent::-webkit-scrollbar,
    .custom-scrollbar::-webkit-scrollbar,
    .classroom-list-container::-webkit-scrollbar {
        width: 6px;
    }

    #modalContent::-webkit-scrollbar-track,
    .custom-scrollbar::-webkit-scrollbar-track,
    .classroom-list-container::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 3px;
    }

    #modalContent::-webkit-scrollbar-thumb,
    .custom-scrollbar::-webkit-scrollbar-thumb,
    .classroom-list-container::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 3px;
    }

    #modalContent::-webkit-scrollbar-thumb:hover,
    .custom-scrollbar::-webkit-scrollbar-thumb:hover,
    .classroom-list-container::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }

    /* Classroom List Container Styling */
    .classroom-list-container {
        scrollbar-width: thin;
        scrollbar-color: #cbd5e1 #f1f5f9;
    }

    /* Enhanced hover effects */
    .classroom-card {
        transition: all 0.3s ease;
    }

    .classroom-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(30, 58, 138, 0.15);
    }

    /* Loading animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .fade-in-up {
        animation: fadeInUp 0.5s ease-out;
    }

    /* Modal Container Responsive Styles - Ultra Wide Coverage */
    .modal-container {
        width: 99vw;
        max-width: none;
        height: calc(100vh - 1rem);
    }

    /* Desktop and larger screens - Maximum width utilization */
    @media (min-width: 1024px) {
        .modal-container {
            width: 98vw;
            max-width: none;
            height: calc(100vh - 1.5rem);
        }
    }

    @media (min-width: 1280px) {
        .modal-container {
            width: 97vw;
            max-width: none;
            height: calc(100vh - 1.5rem);
        }
    }

    @media (min-width: 1536px) {
        .modal-container {
            width: 96vw;
            max-width: none;
            height: calc(100vh - 2rem);
        }
    }

    @media (min-width: 1920px) {
        .modal-container {
            width: 95vw;
            max-width: none;
            height: calc(100vh - 2rem);
        }
    }

    /* Tablet adjustments - Wider coverage */
    @media (max-width: 1023px) and (min-width: 768px) {
        .modal-container {
            width: 98vw;
            max-width: none;
            height: calc(100vh - 1rem);
        }
    }

    /* Mobile adjustments - Full screen coverage */
    @media (max-width: 767px) {
        #classroomModal > div {
            padding: 0.25rem;
        }

        .modal-container {
            width: calc(100vw - 0.5rem);
            height: calc(100vh - 0.5rem);
            max-width: none;
            border-radius: 0.5rem;
        }

        #classroomModal .p-4,
        #classroomModal .p-6 {
            padding: 0.75rem;
        }

        #classroomModal .lg\\:p-5 {
            padding: 1rem;
        }

        #classroomModal .text-xl,
        #classroomModal .text-2xl {
            font-size: 1.25rem;
        }

        /* Reduce modal header padding on mobile */
        #classroomModal .bg-gradient-to-r {
            padding: 0.75rem;
        }
    }

    /* Very small mobile screens - Maximum coverage */
    @media (max-width: 480px) {
        #classroomModal > div {
            padding: 0.125rem;
        }

        .modal-container {
            width: calc(100vw - 0.25rem);
            height: calc(100vh - 0.25rem);
            border-radius: 0.375rem;
        }

        #classroomModal .p-4,
        #classroomModal .p-6 {
            padding: 0.5rem;
        }

        #classroomModal .lg\\:p-5 {
            padding: 0.75rem;
        }

        #classroomModal .text-xl,
        #classroomModal .text-2xl {
            font-size: 1.125rem;
        }

        /* Further reduce modal header padding on very small screens */
        #classroomModal .bg-gradient-to-r {
            padding: 0.5rem;
        }
    }

    /* Utilization bar animation */
    .utilization-bar {
        transition: width 0.5s ease-in-out;
        width: 0%;
    }

    /* Additional Modal Optimizations */
    .modal-container {
        display: flex;
        flex-direction: column;
    }

    /* Optimize modal content for better space utilization */
    #modalContent {
        flex: 1;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: #cbd5e1 #f1f5f9;
    }

    /* Compact scrollbar for modal content */
    #modalContent::-webkit-scrollbar {
        width: 4px;
    }

    #modalContent::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 2px;
    }

    #modalContent::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 2px;
    }

    #modalContent::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }

    /* Optimize text rendering for better readability in compact space */
    .modal-container * {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* Ensure modal takes full available space */
    @media (min-width: 768px) {
        .modal-container {
            min-height: calc(100vh - 2rem);
        }
    }

    @media (max-width: 767px) {
        .modal-container {
            min-height: calc(100vh - 1rem);
        }
    }
</style>

<script>
    // Set utilization bar widths after page load
    document.addEventListener('DOMContentLoaded', function() {
        const utilizationBars = document.querySelectorAll('.utilization-bar');
        utilizationBars.forEach(bar => {
            const utilization = bar.getAttribute('data-utilization');
            setTimeout(() => {
                bar.style.width = utilization + '%';
            }, 100);
        });
    });
</script>


