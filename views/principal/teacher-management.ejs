<!-- Teacher Management Overview -->
<div class="mb-8">
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-principal-light text-principal-primary">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Teachers</p>
                    <p class="text-2xl font-bold text-gray-900"><%= teachers.length %></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">High Performers</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= teachers.filter(t => (t.completion_rate || 0) >= 80).length %>
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-exclamation-triangle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Need Attention</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= teachers.filter(t => (t.completion_rate || 0) < 60).length %>
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                    <i class="fas fa-clock text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Overdue Tasks</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= teachers.reduce((sum, t) => sum + (t.overdue_lectures || 0), 0) %>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Teacher Performance Table -->
<div class="bg-white rounded-lg shadow-md">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h2 class="text-xl font-bold text-gray-900">Teacher Performance Dashboard</h2>
            <div class="flex items-center space-x-4">
                <!-- Export Button -->
                <button class="btn-principal-outline px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fas fa-download mr-2"></i>
                    Export Report
                </button>
                <!-- Refresh Button -->
                <button onclick="refreshTeacherData()" class="btn-principal px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <div class="p-6">
        <!-- Search and Filter -->
        <div class="mb-6 flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                <input type="text" id="search-teachers" placeholder="Search teachers by name or email..."
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
            </div>
            <div class="flex gap-2">
                <select id="filter-performance" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
                    <option value="">All Performance</option>
                    <option value="excellent">Excellent (80%+)</option>
                    <option value="good">Good (60-79%)</option>
                    <option value="average">Average (40-59%)</option>
                    <option value="poor">Poor (<40%)</option>
                </select>
            </div>
        </div>

        <% if (teachers && teachers.length > 0) { %>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 table-principal">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Teacher
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Lectures
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Completion Rate
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Overdue
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Last Login
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Performance
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="teachers-table-body">
                        <% teachers.forEach(teacher => { %>
                            <%
                                const completionRate = teacher.completion_rate || 0;
                                const performanceLevel = completionRate >= 80 ? 'excellent' : completionRate >= 60 ? 'good' : completionRate >= 40 ? 'average' : 'poor';
                            %>
                            <tr class="hover:bg-gray-50 teacher-row"
                                data-name="<%= teacher.name.toLowerCase() %>"
                                data-email="<%= teacher.email.toLowerCase() %>"
                                data-performance="<%= performanceLevel %>">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-principal-light flex items-center justify-center">
                                                <span class="text-sm font-medium text-principal-primary">
                                                    <%= teacher.name.split(' ').map(n => n[0]).join('').toUpperCase() %>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <%= teacher.name %>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <%= teacher.email %>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <span class="font-medium"><%= teacher.delivered_lectures || 0 %></span>/<%= teacher.total_lectures || 0 %>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        Total lectures
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-3">
                                            <div class="h-2 rounded-full <%= completionRate >= 80 ? 'bg-green-500' : completionRate >= 60 ? 'bg-blue-500' : completionRate >= 40 ? 'bg-yellow-500' : 'bg-red-500' %>"
                                                 style="width: <%= Math.min(completionRate, 100) %>%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">
                                            <%= parseFloat(completionRate || 0).toFixed(1) %>%
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <% if (teacher.overdue_lectures > 0) { %>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>
                                            <%= teacher.overdue_lectures %>
                                        </span>
                                    <% } else { %>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check mr-1"></i>
                                            Up to date
                                        </span>
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <% if (teacher.last_login) { %>
                                        <%= new Date(teacher.last_login).toLocaleDateString() %>
                                    <% } else { %>
                                        Never
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= completionRate >= 80 ? 'bg-green-100 text-green-800' : completionRate >= 60 ? 'bg-blue-100 text-blue-800' : completionRate >= 40 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
                                        <% if (completionRate >= 80) { %>
                                            <i class="fas fa-star mr-1"></i> Excellent
                                        <% } else if (completionRate >= 60) { %>
                                            <i class="fas fa-thumbs-up mr-1"></i> Good
                                        <% } else if (completionRate >= 40) { %>
                                            <i class="fas fa-minus-circle mr-1"></i> Average
                                        <% } else { %>
                                            <i class="fas fa-times-circle mr-1"></i> Poor
                                        <% } %>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewTeacherDetails(<%= teacher.id %>)"
                                                class="text-principal-primary hover:text-principal-dark">
                                            View
                                        </button>
                                        <button onclick="sendMessage(<%= teacher.id %>)"
                                                class="text-blue-600 hover:text-blue-900">
                                            Message
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } else { %>
            <div class="text-center py-12">
                <i class="fas fa-user-friends text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900">No teachers found</h3>
                <p class="text-sm text-gray-500">Teacher data will appear here once teachers are added to the system.</p>
            </div>
        <% } %>
    </div>
</div>

<script>
    // Search and filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('search-teachers');
        const performanceFilter = document.getElementById('filter-performance');
        const rows = document.querySelectorAll('.teacher-row');

        function filterTable() {
            const searchTerm = searchInput.value.toLowerCase();
            const performanceValue = performanceFilter.value;

            rows.forEach(row => {
                const name = row.dataset.name;
                const email = row.dataset.email;
                const performance = row.dataset.performance;

                const matchesSearch = name.includes(searchTerm) || email.includes(searchTerm);
                const matchesPerformance = !performanceValue || performance === performanceValue;

                row.style.display = matchesSearch && matchesPerformance ? '' : 'none';
            });
        }

        if (searchInput) searchInput.addEventListener('input', filterTable);
        if (performanceFilter) performanceFilter.addEventListener('change', filterTable);
    });

    function viewTeacherDetails(teacherId) {
        // Implement view teacher details functionality
        window.location.href = `/teacher/profile/${teacherId}`;
    }

    function sendMessage(teacherId) {
        // Implement send message functionality
        console.log(`Sending message to teacher ID: ${teacherId}`);
    }

    function refreshTeacherData() {
        // Refresh the page to get latest data
        window.location.reload();
    }

    // Auto-refresh teacher performance data
    function refreshPageData() {
        fetch('/principal/api/teacher-performance')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Teacher performance data updated:', data.data);
                    // Update the table without full page reload if needed
                }
            })
            .catch(error => {
                console.error('Error refreshing teacher performance:', error);
            });
    }

    // Start auto-refresh every 5 minutes
    startAutoRefresh(300000);
</script>
