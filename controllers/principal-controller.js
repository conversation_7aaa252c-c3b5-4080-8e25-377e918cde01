/**
 * Principal Controller
 * Handles all principal dashboard functionality and school oversight
 */

const db = require('../config/database');

// Principal Dashboard
exports.getDashboard = async (req, res) => {
  try {
    // Get overall school statistics
    const [schoolStats] = await db.query(`
      SELECT
        (SELECT COUNT(*) FROM users WHERE role = 'teacher' AND is_active = 1) as total_teachers,
        (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = 1) as total_students,
        (SELECT COUNT(*) FROM classes) as total_classes,
        (SELECT COUNT(*) FROM subjects) as total_subjects
    `);

    // Get today's lecture statistics
    const [todayStats] = await db.query(`
      SELECT
        COUNT(*) as total_lectures_today,
        SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_lectures,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_lectures,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_lectures
      FROM teacher_lectures
      WHERE date = CURDATE()
    `);

    // Get overall syllabus completion
    const [syllabusStats] = await db.query(`
      SELECT
        COUNT(*) as total_topics,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_topics,
        ROUND((SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as completion_percentage
      FROM teacher_lectures
    `);

    // Get recent activities
    const [recentActivities] = await db.query(`
      SELECT
        'lecture' as type,
        tl.topic as title,
        tl.subject_name as subtitle,
        u.name as teacher_name,
        tl.date,
        tl.status,
        tl.created_at
      FROM teacher_lectures tl
      JOIN users u ON tl.teacher_id = u.id
      ORDER BY tl.created_at DESC
      LIMIT 10
    `);

    // Get class-wise performance summary
    const [classPerformance] = await db.query(`
      SELECT
        class_name,
        COUNT(*) as total_lectures,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_lectures,
        ROUND((SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as completion_rate
      FROM teacher_lectures
      GROUP BY class_name
      ORDER BY completion_rate DESC
      LIMIT 5
    `);

    // Get teacher performance summary
    const [teacherPerformance] = await db.query(`
      SELECT
        u.name as teacher_name,
        COUNT(tl.id) as total_lectures,
        SUM(CASE WHEN tl.status = 'delivered' THEN 1 ELSE 0 END) as delivered_lectures,
        ROUND((SUM(CASE WHEN tl.status = 'delivered' THEN 1 ELSE 0 END) / COUNT(tl.id)) * 100, 2) as completion_rate
      FROM users u
      LEFT JOIN teacher_lectures tl ON u.id = tl.teacher_id
      WHERE u.role = 'teacher' AND u.is_active = 1
      GROUP BY u.id, u.name
      HAVING COUNT(tl.id) > 0
      ORDER BY completion_rate DESC
      LIMIT 5
    `);

    // Get upcoming events/deadlines
    const [upcomingEvents] = await db.query(`
      SELECT
        'lecture' as type,
        topic as title,
        subject_name as subtitle,
        date,
        start_time,
        class_name
      FROM teacher_lectures
      WHERE date >= CURDATE() AND status = 'pending'
      ORDER BY date ASC, start_time ASC
      LIMIT 8
    `);

    const stats = {
      ...schoolStats[0],
      ...todayStats[0],
      ...syllabusStats[0]
    };

    // Set the layout explicitly for this response
    res.locals.layout = 'layouts/principal';

    console.log('Principal Dashboard - Layout set to:', res.locals.layout);

    res.render('principal/dashboard', {
      title: 'Principal Command Center',
      layout: 'layouts/principal',
      currentPage: 'dashboard',
      stats,
      recentActivities,
      classPerformance,
      teacherPerformance,
      upcomingEvents,
      formatDate: (date) => {
        if (!date) return '';
        return new Date(date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      },
      formatTime: (time) => {
        if (!time) return '';
        return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
      },
      getStatusColor: (status) => {
        const colors = {
          'delivered': 'text-green-600',
          'completed': 'text-green-600',
          'pending': 'text-yellow-600',
          'cancelled': 'text-red-600',
          'overdue': 'text-red-600'
        };
        return colors[status] || 'text-gray-600';
      },
      getStatusBadge: (status) => {
        const badges = {
          'delivered': 'bg-green-100 text-green-800',
          'completed': 'bg-green-100 text-green-800',
          'pending': 'bg-yellow-100 text-yellow-800',
          'cancelled': 'bg-red-100 text-red-800',
          'overdue': 'bg-red-100 text-red-800'
        };
        return badges[status] || 'bg-gray-100 text-gray-800';
      }
    });
  } catch (error) {
    console.error('Error loading principal dashboard:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load principal dashboard',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Academic Progress
exports.getAcademicProgress = async (req, res) => {
  try {
    // Get class-wise syllabus completion - simplified query
    const [classProgress] = await db.query(`
      SELECT
        tl.class_name,
        tl.subject_name,
        COUNT(tl.id) as total_topics,
        SUM(CASE WHEN tl.status = 'completed' THEN 1 ELSE 0 END) as completed_topics,
        ROUND((SUM(CASE WHEN tl.status = 'completed' THEN 1 ELSE 0 END) / COUNT(tl.id)) * 100, 2) as completion_percentage,
        u.name as teacher_name
      FROM teacher_lectures tl
      LEFT JOIN users u ON tl.teacher_id = u.id
      GROUP BY tl.class_name, tl.subject_name, u.name
      ORDER BY tl.class_name, tl.subject_name
    `);

    // Get subject-wise progress
    const [subjectProgress] = await db.query(`
      SELECT
        subject_name,
        COUNT(*) as total_topics,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_topics,
        ROUND((SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as completion_percentage
      FROM teacher_lectures
      GROUP BY subject_name
      ORDER BY completion_percentage DESC
    `);

    // Set the layout explicitly for this response
    res.locals.layout = 'layouts/principal';

    res.render('principal/academic-progress', {
      title: 'Academic Intelligence',
      layout: 'layouts/principal',
      currentPage: 'academic-progress',
      classProgress,
      subjectProgress
    });
  } catch (error) {
    console.error('Error loading academic progress:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load academic progress',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Teacher Management
exports.getTeacherManagement = async (req, res) => {
  try {
    // Get teacher performance data
    const [teachers] = await db.query(`
      SELECT
        u.id,
        u.name,
        u.email,
        u.full_name,
        COUNT(tl.id) as total_lectures,
        SUM(CASE WHEN tl.status = 'delivered' THEN 1 ELSE 0 END) as delivered_lectures,
        SUM(CASE WHEN tl.status = 'pending' AND tl.date < CURDATE() THEN 1 ELSE 0 END) as overdue_lectures,
        ROUND((SUM(CASE WHEN tl.status = 'delivered' THEN 1 ELSE 0 END) / COUNT(tl.id)) * 100, 2) as completion_rate,
        u.last_login
      FROM users u
      LEFT JOIN teacher_lectures tl ON u.id = tl.teacher_id
      WHERE u.role = 'teacher' AND u.is_active = 1
      GROUP BY u.id, u.name, u.email, u.full_name, u.last_login
      ORDER BY completion_rate DESC
    `);

    // Set the layout explicitly for this response
    res.locals.layout = 'layouts/principal';

    res.render('principal/teacher-management', {
      title: 'Faculty Operations',
      layout: 'layouts/principal',
      currentPage: 'teacher-management',
      teachers
    });
  } catch (error) {
    console.error('Error loading teacher management:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load teacher management',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Teacher Timetables
exports.getTeacherTimetables = async (req, res) => {
  try {
    // Get teacher timetable data
    const [timetables] = await db.query(`
      SELECT
        u.name as teacher_name,
        tl.date,
        tl.start_time,
        tl.end_time,
        tl.subject_name,
        tl.class_name,
        tl.topic,
        tl.status
      FROM teacher_lectures tl
      JOIN users u ON tl.teacher_id = u.id
      WHERE tl.date >= CURDATE()
      ORDER BY tl.date ASC, tl.start_time ASC
    `);

    // Set the layout explicitly for this response
    res.locals.layout = 'layouts/principal';

    res.render('principal/teacher-timetables', {
      title: 'Strategic Calendar',
      layout: 'layouts/principal',
      currentPage: 'teacher-timetables',
      timetables
    });
  } catch (error) {
    console.error('Error loading teacher timetables:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load teacher timetables',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Student Analytics
exports.getStudentAnalytics = async (req, res) => {
  try {
    // Get student statistics
    const [studentStats] = await db.query(`
      SELECT
        (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = 1) as total_students,
        (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = 1 AND last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as active_students,
        (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = 0) as inactive_students
    `);

    res.render('principal/student-analytics', {
      title: 'Student Analytics',
      layout: 'layouts/principal',
      currentPage: 'student-analytics',
      studentStats: studentStats[0]
    });
  } catch (error) {
    console.error('Error loading student analytics:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load student analytics',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Infrastructure Overview
exports.getInfrastructure = async (req, res) => {
  try {
    // First, update all room capacities to 50 if they're not already
    await db.query(`
      UPDATE rooms SET capacity = 50 WHERE capacity != 50
    `);

    // Get actual classroom data with room information including capacity
    const [classroomData] = await db.query(`
      SELECT
        r.id as room_id,
        r.room_number,
        r.floor,
        r.capacity,
        c.id as classroom_id,
        c.session,
        cl.grade,
        cl.section,
        t.name as trade_name,
        u.name as incharge_name,
        COUNT(sc.student_id) as student_count,
        ROUND((COUNT(sc.student_id) / r.capacity) * 100, 1) as utilization_percentage
      FROM rooms r
      LEFT JOIN classrooms c ON r.id = c.room_id AND c.is_active = 1
      LEFT JOIN classes cl ON c.class_id = cl.id
      LEFT JOIN trades t ON c.trade_id = t.id
      LEFT JOIN users u ON c.incharge = u.id
      LEFT JOIN student_classrooms sc ON c.id = sc.classroom_id AND sc.status = 'active'
      WHERE r.room_number LIKE 'Room %'
      GROUP BY r.id, r.room_number, r.floor, r.capacity, c.id, c.session, cl.grade, cl.section, t.name, u.name
      ORDER BY r.id
    `);

    // Get Computer Labs with equipment details
    const [computerLabs] = await db.query(`
      SELECT
        r.id as room_id,
        r.room_number,
        r.capacity,
        r.building,
        r.floor,
        COUNT(DISTINCT i.item_id) as total_equipment,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN i.item_id END) as printers,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN i.item_id END) as projectors,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%EZVIZ%' OR i.name LIKE '%Camera%' OR i.model LIKE '%Camera%' THEN i.item_id END) as cameras,
        MIN(i.purchase_date) as earliest_purchase,
        MAX(i.purchase_date) as latest_purchase,
        MIN(i.created_at) as earliest_installation,
        MAX(i.created_at) as latest_installation
      FROM rooms r
      LEFT JOIN inventory_items i ON r.id = i.room_id
      WHERE r.room_number LIKE 'Computer Lab%'
      GROUP BY r.id, r.room_number, r.capacity, r.building, r.floor
      ORDER BY r.room_number
    `);

    // Get Science Labs with equipment details
    const [scienceLabs] = await db.query(`
      SELECT
        r.id as room_id,
        r.room_number,
        r.capacity,
        r.building,
        r.floor,
        COUNT(DISTINCT i.item_id) as total_equipment,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN i.item_id END) as projectors,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN i.item_id END) as printers,
        MIN(i.purchase_date) as earliest_purchase,
        MAX(i.purchase_date) as latest_purchase,
        MIN(i.created_at) as earliest_installation,
        MAX(i.created_at) as latest_installation
      FROM rooms r
      LEFT JOIN inventory_items i ON r.id = i.room_id
      WHERE r.room_number IN ('Biology Lab', 'Chemistry Lab', 'Physics Lab')
      GROUP BY r.id, r.room_number, r.capacity, r.building, r.floor
      ORDER BY r.room_number
    `);

    // Get Library with equipment details
    const [library] = await db.query(`
      SELECT
        r.id as room_id,
        r.room_number,
        r.capacity,
        r.building,
        r.floor,
        COUNT(DISTINCT i.item_id) as total_equipment,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN i.item_id END) as printers,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units
      FROM rooms r
      LEFT JOIN inventory_items i ON r.id = i.room_id
      WHERE r.room_number = 'Library'
      GROUP BY r.id, r.room_number, r.capacity, r.building, r.floor
    `);

    // Get Offices with equipment details
    const [offices] = await db.query(`
      SELECT
        r.id as room_id,
        r.room_number,
        r.capacity,
        r.building,
        r.floor,
        COUNT(DISTINCT i.item_id) as total_equipment,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Laptop%' THEN i.item_id END) as laptops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN i.item_id END) as printers,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%iPad%' OR i.name LIKE '%Tablet%' THEN i.item_id END) as tablets
      FROM rooms r
      LEFT JOIN inventory_items i ON r.id = i.room_id
      WHERE r.room_number LIKE '%Office%' AND r.room_number NOT LIKE '%Hostel%' AND r.room_number NOT LIKE '%Computer Office%'
      GROUP BY r.id, r.room_number, r.capacity, r.building, r.floor
      ORDER BY r.room_number
    `);

    // Get Hostel Warden Rooms with equipment details
    const [hostelRooms] = await db.query(`
      SELECT
        r.id as room_id,
        r.room_number,
        r.capacity,
        r.building,
        r.floor,
        COUNT(DISTINCT i.item_id) as total_equipment,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN i.item_id END) as desktops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Laptop%' THEN i.item_id END) as laptops,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN i.item_id END) as printers,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%UPS%' THEN i.item_id END) as ups_units,
        COUNT(DISTINCT CASE WHEN i.name LIKE '%BDL%' OR i.name LIKE '%PHILIPS%' OR i.name LIKE '%TV%' THEN i.item_id END) as tvs,
        MIN(i.purchase_date) as earliest_purchase,
        MAX(i.purchase_date) as latest_purchase,
        MIN(i.created_at) as earliest_installation,
        MAX(i.created_at) as latest_installation
      FROM rooms r
      LEFT JOIN inventory_items i ON r.id = i.room_id
      WHERE r.room_number LIKE '%Hostel Warden Room%'
      GROUP BY r.id, r.room_number, r.capacity, r.building, r.floor
      ORDER BY r.room_number
    `);

    // Get electrical inventory summary
    const [electricalSummary] = await db.query(`
      SELECT
        COUNT(*) as total_items,
        SUM(CASE WHEN status = 'working' THEN 1 ELSE 0 END) as working_items,
        SUM(CASE WHEN status = 'faulty' THEN 1 ELSE 0 END) as faulty_items
      FROM electrical_inventory
    `);

    res.render('principal/infrastructure', {
      title: 'Infrastructure Command Center',
      layout: 'layouts/principal',
      currentPage: 'infrastructure',
      classroomData,
      computerLabs,
      scienceLabs,
      library: library[0] || null,
      offices,
      hostelRooms,
      electricalSummary: electricalSummary[0] || { total_items: 0, working_items: 0, faulty_items: 0 }
    });
  } catch (error) {
    console.error('Error loading infrastructure:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load infrastructure overview',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Reports
exports.getReports = async (req, res) => {
  try {
    res.render('principal/reports', {
      title: 'Reports & Analytics',
      layout: 'layouts/principal',
      currentPage: 'reports'
    });
  } catch (error) {
    console.error('Error loading reports:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load reports',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Academic Reports
exports.getAcademicReports = async (req, res) => {
  try {
    res.render('principal/reports/academic', {
      title: 'Academic Reports',
      layout: 'layouts/principal',
      currentPage: 'reports'
    });
  } catch (error) {
    console.error('Error loading academic reports:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load academic reports',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Attendance Reports
exports.getAttendanceReports = async (req, res) => {
  try {
    res.render('principal/reports/attendance', {
      title: 'Attendance Reports',
      layout: 'layouts/principal',
      currentPage: 'reports'
    });
  } catch (error) {
    console.error('Error loading attendance reports:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load attendance reports',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};

// Performance Reports
exports.getPerformanceReports = async (req, res) => {
  try {
    res.render('principal/reports/performance', {
      title: 'Performance Reports',
      layout: 'layouts/principal',
      currentPage: 'reports'
    });
  } catch (error) {
    console.error('Error loading performance reports:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load performance reports',
      error: { status: 500 },
      layout: 'layouts/main'
    });
  }
};
